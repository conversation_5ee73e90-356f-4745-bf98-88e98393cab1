name: 'Build and Push Docker Image'
description: 'Build and push Docker images for a service'
inputs:
  service-name:
    description: 'Name of the service'
    required: true
  registry:
    description: 'Container registry URL'
    required: false
    default: 'ghcr.io'
  repository:
    description: 'Repository name'
    required: true
  tags:
    description: 'Image tags (can be multiple, one per line)'
    required: false
    default: 'latest'
  push:
    description: 'Whether to push the image'
    required: false
    default: 'true'
  platforms:
    description: 'Target platforms'
    required: false
    default: 'linux/amd64'

outputs:
  image-api-tags:
    description: 'API image names with tags'
    value: ${{ steps.build-info.outputs.image-api-tags }}
  image-worker-tags:
    description: 'Worker image names with tags'
    value: ${{ steps.build-info.outputs.image-worker-tags }}
  api-built:
    description: 'Whether API image was built'
    value: ${{ steps.build-info.outputs.api-built }}
  worker-built:
    description: 'Whether Worker image was built'
    value: ${{ steps.build-info.outputs.worker-built }}

runs:
  using: 'composite'
  steps:
    - name: Check service and prepare build info
      id: build-info
      shell: bash
      run: |
        SERVICE_DIR="services/${{ inputs.service-name }}"

        if [ ! -d "$SERVICE_DIR" ]; then
          echo "❌ Service directory $SERVICE_DIR does not exist"
          exit 1
        fi

        # Check what components exist
        API_EXISTS=false
        WORKER_EXISTS=false

        if [ -d "$SERVICE_DIR/cmd/api" ] && [ -f "$SERVICE_DIR/cmd/api/Dockerfile" ]; then
          API_EXISTS=true
          echo "📡 API Dockerfile found"
        fi

        if [ -d "$SERVICE_DIR/cmd/worker" ] && [ -f "$SERVICE_DIR/cmd/worker/Dockerfile" ]; then
          WORKER_EXISTS=true
          echo "⚙️ Worker Dockerfile found"
        fi

        # Set outputs
        echo "api-exists=$API_EXISTS" >> $GITHUB_OUTPUT
        echo "worker-exists=$WORKER_EXISTS" >> $GITHUB_OUTPUT

        # Prepare image names with tags
        REGISTRY="${{ inputs.registry }}"
        REPO="${{ inputs.repository }}"
        TAGS="${{ inputs.tags }}"

        # Convert tags to proper format for each image type
        API_TAGS=""
        WORKER_TAGS=""

        # Process each tag and create full image names
        while IFS= read -r tag; do
          if [ -n "$tag" ]; then
            if [ -n "$API_TAGS" ]; then
              API_TAGS="$API_TAGS,$REGISTRY/$REPO/torra-${{ inputs.service-name }}-api:$tag"
              WORKER_TAGS="$WORKER_TAGS,$REGISTRY/$REPO/torra-${{ inputs.service-name }}-worker:$tag"
            else
              API_TAGS="$REGISTRY/$REPO/torra-${{ inputs.service-name }}-api:$tag"
              WORKER_TAGS="$REGISTRY/$REPO/torra-${{ inputs.service-name }}-worker:$tag"
            fi
          fi
        done <<< "$TAGS"

        echo "image-api-tags=$API_TAGS" >> $GITHUB_OUTPUT
        echo "image-worker-tags=$WORKER_TAGS" >> $GITHUB_OUTPUT

        echo "🏗️ Prepared to build images for ${{ inputs.service-name }}"
        if [ "$API_EXISTS" = "true" ]; then
          echo "  📡 API: $API_IMAGE"
        fi
        if [ "$WORKER_EXISTS" = "true" ]; then
          echo "  ⚙️ Worker: $WORKER_IMAGE"
        fi

    - name: Set up Docker Buildx
      if: steps.build-info.outputs.api-exists == 'true' || steps.build-info.outputs.worker-exists == 'true'
      uses: docker/setup-buildx-action@v3

    - name: Build and push API image
      if: steps.build-info.outputs.api-exists == 'true'
      uses: docker/build-push-action@v5
      with:
        context: services/${{ inputs.service-name }}
        file: services/${{ inputs.service-name }}/cmd/api/Dockerfile
        platforms: ${{ inputs.platforms }}
        push: ${{ inputs.push }}
        tags: ${{ steps.build-info.outputs.image-api }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        labels: |
          org.opencontainers.image.title=MyTorra ${{ inputs.service-name }} API
          org.opencontainers.image.description=API service for ${{ inputs.service-name }}
          org.opencontainers.image.source=${{ github.server_url }}/${{ github.repository }}
          org.opencontainers.image.revision=${{ github.sha }}

    - name: Build and push Worker image
      if: steps.build-info.outputs.worker-exists == 'true'
      uses: docker/build-push-action@v5
      with:
        context: services/${{ inputs.service-name }}
        file: services/${{ inputs.service-name }}/cmd/worker/Dockerfile
        platforms: ${{ inputs.platforms }}
        push: ${{ inputs.push }}
        tags: ${{ steps.build-info.outputs.image-worker }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        labels: |
          org.opencontainers.image.title=MyTorra ${{ inputs.service-name }} Worker
          org.opencontainers.image.description=Worker service for ${{ inputs.service-name }}
          org.opencontainers.image.source=${{ github.server_url }}/${{ github.repository }}
          org.opencontainers.image.revision=${{ github.sha }}

    - name: Set final outputs
      shell: bash
      run: |
        echo "api-built=${{ steps.build-info.outputs.api-exists }}" >> $GITHUB_OUTPUT
        echo "worker-built=${{ steps.build-info.outputs.worker-exists }}" >> $GITHUB_OUTPUT

        echo "✅ Build summary for ${{ inputs.service-name }}:"
        if [ "${{ steps.build-info.outputs.api-exists }}" = "true" ]; then
          echo "  📡 API image: ${{ steps.build-info.outputs.image-api }}"
        fi
        if [ "${{ steps.build-info.outputs.worker-exists }}" = "true" ]; then
          echo "  ⚙️ Worker image: ${{ steps.build-info.outputs.image-worker }}"
        fi
