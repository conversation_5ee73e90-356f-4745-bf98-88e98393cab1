name: CI Pipeline

on:
  push:
    branches: [main, develop, feature/*]
  pull_request:
    branches: [main, develop]
  workflow_dispatch:

permissions:
  contents: read
  actions: write
  security-events: write

env:
  GO_VERSION: "1.25"
  NODE_VERSION: "20"

jobs:
  # Detect existing services dynamically
  detect-services:
    name: Detect Services
    runs-on: arc-runner-set
    outputs:
      services: ${{ steps.detect.outputs.services }}
      services-count: ${{ steps.detect.outputs.count }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Detect existing services
        id: detect
        run: |
          # Make script executable and run it
          chmod +x .github/scripts/detect-services.sh
          SERVICES_JSON=$(.github/scripts/detect-services.sh json)
          SERVICES_COUNT=$(echo "$SERVICES_JSON" | jq length)

          echo "services=$SERVICES_JSON" >> $GITHUB_OUTPUT
          echo "count=$SERVICES_COUNT" >> $GITHUB_OUTPUT

          echo "🎯 Detected $SERVICES_COUNT service(s) to build"
          echo "📋 Services: $SERVICES_JSON"

  # Build services dynamically
  build:
    name: Build Services
    runs-on: arc-runner-set
    needs: detect-services
    if: needs.detect-services.outputs.services-count > 0
    strategy:
      matrix:
        service: ${{ fromJson(needs.detect-services.outputs.services) }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Go Environment
        uses: ./.github/actions/setup-go-env
        with:
          go-version: ${{ env.GO_VERSION }}
          cache-key-suffix: "build-${{ matrix.service }}"

      - name: Build service
        uses: ./.github/actions/build-service
        with:
          service-name: ${{ matrix.service }}
          go-version: ${{ env.GO_VERSION }}
          build-version: ${{ github.sha }}

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.service }}-binaries
          path: bin/${{ matrix.service }}-*
          retention-days: 7

  # Test job - runs all tests
  test:
    name: Run Tests
    runs-on: arc-runner-set
    needs: build
    # Note: Services require Docker which is not available on arc-runner-set
    # Integration tests requiring database/redis should be run separately

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}
          cache: true

      - name: Setup test environment
        if: false
        run: |
          cp .env.example .env.test
          echo "MYTORRA_DATABASE_HOST=localhost" >> .env.test
          echo "MYTORRA_DATABASE_USER=test" >> .env.test
          echo "MYTORRA_DATABASE_PASSWORD=test" >> .env.test
          echo "MYTORRA_DATABASE_NAME=testdb" >> .env.test
          echo "MYTORRA_REDIS_HOST=localhost" >> .env.test

      - name: Run unit tests
        run: |
          # Make script executable and run it
          chmod +x .github/scripts/run-tests.sh
          .github/scripts/run-tests.sh
        env:
          MYTORRA_ENVIRONMENT: test

      # TODO: Enable when integration tests are implemented
      # - name: Run integration tests
      #   run: |
      #     CGO_ENABLED=1 go test -v -race -tags=integration ./tests/integration/...
      #   env:
      #     MYTORRA_ENVIRONMENT: test

      # TODO: Enable when CODECOV_TOKEN is configured
      # - name: Upload coverage reports
      #   uses: codecov/codecov-action@v4
      #   with:
      #     token: ${{ secrets.CODECOV_TOKEN }}
      #     file: ./coverage.txt
      #     flags: unittests
      #     name: codecov-umbrella

      - name: Generate test report
        if: always()
        run: |
          # Install junit report generator
          go install github.com/jstemmer/go-junit-report/v2@latest

          # Run tests for all modules and generate report
          if [ -f go.work ]; then
            echo "Running tests in workspace mode for report..."
            TEST_OUTPUT=""
            for module in $(grep -E '^\s*\./.*' go.work | sed 's/^\s*\.\///');
            do
              if [ -d "$module" ] && [ -f "$module/go.mod" ]; then
                echo "Testing module: $module"
                MODULE_OUTPUT=$(cd "$module" && CGO_ENABLED=1 go test -v ./... 2>&1 || true)
                TEST_OUTPUT="${TEST_OUTPUT}\n${MODULE_OUTPUT}"
              fi
            done
            echo -e "$TEST_OUTPUT" | $(go env GOPATH)/bin/go-junit-report > test-report.xml
          else
            # Single module mode - but we should always have go.work in this project
            echo "Warning: No go.work file found, skipping test report generation"
            echo '<?xml version="1.0" encoding="UTF-8"?><testsuites></testsuites>' > test-report.xml
          fi

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results
          path: test-report.xml

  # API Documentation generation and validation
  docs:
    name: API Documentation
    runs-on: arc-runner-set
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go Environment
        uses: ./.github/actions/setup-go-env
        with:
          go-version: ${{ env.GO_VERSION }}
          cache-key-suffix: "docs"

      - name: Setup API Documentation
        uses: ./.github/actions/setup-api-docs
        with:
          validate-docs: "true"
          check-up-to-date: "true"

      - name: Upload API documentation
        uses: actions/upload-artifact@v4
        with:
          name: api-documentation
          path: docs/api/
          retention-days: 30

  # i18n validation
  i18n:
    name: i18n Validation
    runs-on: arc-runner-set
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go Environment
        uses: ./.github/actions/setup-go-env
        with:
          go-version: ${{ env.GO_VERSION }}
          cache-key-suffix: "i18n"

      - name: Setup i18n Validation
        uses: ./.github/actions/setup-i18n-validation
        with:
          fail-on-missing: "true"
          check-unused: "true"

  # Lint job - code quality checks
  lint:
    name: Lint Code
    runs-on: arc-runner-set
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Setup and Run golangci-lint
        uses: ./.github/actions/setup-golangci-lint
        with:
          config-file: ".golangci.yml"
          timeout: "10m"

      - name: Check Go formatting
        run: |
          if [ -n "$(gofmt -l .)" ]; then
            echo "Go files are not formatted. Please run 'make fmt'"
            gofmt -d .
            exit 1
          fi

      - name: Check Go mod tidy
        run: |
          go mod tidy
          if [ -n "$(git status --porcelain)" ]; then
            echo "go.mod or go.sum is not tidy. Please run 'go mod tidy'"
            git diff
            exit 1
          fi

  # Security scan job
  security:
    name: Security Scan
    runs-on: arc-runner-set
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Install and Run Gosec Security Scanner
        run: |
          # Install gosec
          go install github.com/securego/gosec/v2/cmd/gosec@latest
          # Run gosec and output SARIF format
          $(go env GOPATH)/bin/gosec -no-fail -fmt sarif -out gosec-results.sarif ./... || true

      - name: Upload SARIF file
        if: always() && github.event_name == 'pull_request'
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: gosec-results.sarif
        continue-on-error: true

      - name: Run Nancy vulnerability scanner
        run: |
          go install github.com/sonatype-nexus-community/nancy@latest
          go list -json -deps ./... | nancy sleuth

      - name: Install and Run Trivy vulnerability scanner
        run: |
          # Install trivy to user's local bin directory
          mkdir -p $HOME/.local/bin
          curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b $HOME/.local/bin latest

          # Add to PATH
          export PATH=$HOME/.local/bin:$PATH

          # Run trivy filesystem scan
          trivy fs --severity CRITICAL,HIGH --format sarif --output trivy-results.sarif . || true

          # Also create a human-readable report
          trivy fs --severity CRITICAL,HIGH . || true

      - name: Upload Trivy results
        if: always() && github.event_name == 'pull_request' && hashFiles('trivy-results.sarif') != ''
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: trivy-results.sarif
        continue-on-error: true

  # Docker build and push - uses dynamic service detection
  docker:
    name: Build Docker Images
    runs-on: arc-runner-set
    needs: [build, test, lint, detect-services]
    if: false
    # if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop') && needs.detect-services.outputs.services-count > 0
    strategy:
      matrix:
        service: ${{ fromJson(needs.detect-services.outputs.services) }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata for tags
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository }}/torra-${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-

      - name: Build and push service images
        uses: ./.github/actions/build-push-image
        with:
          service-name: ${{ matrix.service }}
          registry: ghcr.io
          repository: ${{ github.repository }}
          tags: ${{ steps.meta.outputs.tags }}
          push: ${{ github.event_name != 'pull_request' }}
          platforms: linux/amd64

  # Basic cleanup job - keep artifacts management
  cleanup:
    name: Cleanup Resources
    runs-on: arc-runner-set
    needs: [build, test, lint, security, docs, i18n]
    if: always()
    steps:
      - name: Clean workspace
        run: |
          # Clean up temporary files
          rm -rf /tmp/torra-*

      - name: Remove old artifacts
        uses: actions/github-script@v7
        with:
          script: |
            const artifacts = await github.rest.actions.listArtifactsForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              per_page: 100
            });

            const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);

            for (const artifact of artifacts.data.artifacts) {
              if (Date.parse(artifact.created_at) < oneWeekAgo) {
                await github.rest.actions.deleteArtifact({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  artifact_id: artifact.id
                });
              }
            }
